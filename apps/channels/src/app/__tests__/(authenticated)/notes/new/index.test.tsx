import { issueFactory } from '@shape-construction/api/factories/issues';
import { projectFactory } from '@shape-construction/api/factories/projects';
import { userFactory } from '@shape-construction/api/factories/users';
import { getApiProjectsMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { postApiProjectsProjectIdIssuesMockHandler } from '@shape-construction/api/handlers-factories/projects/issues';
import { Toast } from '@shape-construction/arch-ui-native';
import { Text, View } from 'react-native';
import NewNotePage from 'src/app/(authenticated)/notes/new';
import NewNoteLayout from 'src/app/(authenticated)/notes/new/_layout';
import { server } from 'src/tests/mock-server';
import { renderRouter, screen, userEvent } from 'src/tests/test-utils';

describe('<NewNotePage />', () => {
  it('renders the form with default values', async () => {
    const user = userFactory({ defaultProject: 'project-0' });
    server.use(getApiProjectsMockHandler(() => [projectFactory({ id: 'project-0', title: 'Project 0' })]));

    renderRouter(
      {
        '/(authenticated)/notes/new/_layout': NewNoteLayout,
        '/(authenticated)/notes/new/index': NewNotePage,
      },
      {
        initialUrl: '/notes/new',
      },
      { user }
    );

    expect(await screen.findByRole('combobox', { name: 'Project 0' })).toBeOnTheScreen();
    expect(await await screen.findByPlaceholderText('notes.newNote.form.observation.placeholder')).toBeOnTheScreen();
    expect(await screen.findByRole('combobox', { name: 'notes.newNote.types.issue' })).toBeOnTheScreen();
  });

  it('renders the form with a disabled submit button', async () => {
    renderRouter(
      {
        '/(authenticated)/notes/new/_layout': NewNoteLayout,
        '/(authenticated)/notes/new/index': NewNotePage,
      },
      {
        initialUrl: '/notes/new',
      }
    );

    expect(await screen.findByRole('button', { name: 'notes.newNote.form.actions.saveNote' })).toBeDisabled();
  });

  it('submits the form with success', async () => {
    const user = userFactory({ defaultProject: 'project-0' });
    const spyOnToast = jest.spyOn(Toast, 'show');
    server.use(
      getApiProjectsMockHandler(() => [projectFactory({ id: 'project-0', title: 'Project 0' })]),
      postApiProjectsProjectIdIssuesMockHandler(() => issueFactory())
    );
    renderRouter(
      {
        '/': () => (
          <View>
            <Text>Success page</Text>
          </View>
        ),
        '/(authenticated)/notes/new/_layout': NewNoteLayout,
        '/(authenticated)/notes/new/index': NewNotePage,
      },
      {
        initialUrl: '/notes/new',
      },
      { user }
    );

    expect(await screen.findByRole('combobox', { name: 'Project 0' })).toBeOnTheScreen();
    expect(screen.getByPlaceholderText('notes.newNote.form.observation.placeholder')).toBeOnTheScreen();
    expect(screen.getByRole('combobox', { name: 'notes.newNote.types.issue' })).toBeOnTheScreen();

    await userEvent.press(screen.getByRole('text', { name: 'notes.newNote.form.observation.title' }));
    await userEvent.type(screen.getByPlaceholderText('notes.newNote.form.observation.placeholder'), 'Test note');
    await userEvent.press(
      await screen.findByRole('button', { name: 'notes.newNote.form.actions.saveNote', disabled: false })
    );

    expect(spyOnToast).toHaveBeenCalledWith('notes.newNote.form.feedback.success');
    expect(await screen.findByText('Success page')).toBeOnTheScreen();
  });

  it('submit the form with error', async () => {
    const user = userFactory({ defaultProject: 'project-0' });
    const spyOnToast = jest.spyOn(Toast, 'show');
    server.use(
      getApiProjectsMockHandler(() => [projectFactory({ id: 'project-0', title: 'Project 0' })]),
      postApiProjectsProjectIdIssuesMockHandler(undefined, { status: 400 })
    );
    renderRouter(
      {
        '/(authenticated)/notes/new/_layout': NewNoteLayout,
        '/(authenticated)/notes/new/index': NewNotePage,
      },
      {
        initialUrl: '/notes/new',
      },
      { user }
    );

    expect(await screen.findByRole('combobox', { name: 'Project 0' })).toBeOnTheScreen();
    expect(screen.getByPlaceholderText('notes.newNote.form.observation.placeholder')).toBeOnTheScreen();
    expect(screen.getByRole('combobox', { name: 'notes.newNote.types.issue' })).toBeOnTheScreen();

    await userEvent.press(await screen.findByRole('text', { name: 'notes.newNote.form.observation.title' }));
    await userEvent.type(screen.getByPlaceholderText('notes.newNote.form.observation.placeholder'), 'Test note');
    await userEvent.press(
      await screen.findByRole('button', { name: 'notes.newNote.form.actions.saveNote', disabled: false })
    );

    expect(spyOnToast).toHaveBeenCalledWith('notes.newNote.form.feedback.error');
    expect(screen).toHavePathname('/notes/new');
  });
});
