import { BottomSheet } from '@shape-construction/arch-ui-native';
import { CogIcon } from '@shape-construction/arch-ui-native/src/Icons/outline';
import { ChatBubbleLeftRightIcon, PlusCircleIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { theme } from '@shape-construction/arch-ui-native/src/theme';
import { Link, Tabs } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { Platform, StyleSheet, Text, TouchableOpacity } from 'react-native';
import { CreateChannelBottomSheet } from 'src/components/Layout/CreateChannelBottomSheet';
import { environment } from 'src/config/environment';

const styles = StyleSheet.create({
  tabBarStyle: {
    backgroundColor: theme.colors['gray-50'],
    height: Platform.OS === 'ios' || !environment.FEATURE_FLAG_CAPTURE_NOTES ? 100 : 140,
    paddingTop: environment.FEATURE_FLAG_CAPTURE_NOTES ? 10 : undefined,
  },
  tabBarLabelStyle: {
    fontWeight: '500',
    fontSize: 12,
  },
});

export const TabsLayout = () => {
  const { t } = useTranslation();

  return (
    <BottomSheet.Root>
      <Tabs
        screenOptions={{
          headerShadowVisible: false,
          tabBarInactiveTintColor: theme.colors['gray-500'],
          tabBarActiveTintColor: theme.colors['indigo-500'],
          tabBarLabelStyle: styles.tabBarLabelStyle,
          tabBarStyle: styles.tabBarStyle,
        }}
      >
        <Tabs.Screen
          name="index"
          options={{
            tabBarLabel: t('channelList.title'),
            headerTitle: () => <Text className="text-lg leading-6 font-bold text-brand">{t('channelList.title')}</Text>,
            tabBarIcon: ChatBubbleLeftRightIcon,
            headerTitleAlign: 'left',
            headerRight: () => {
              if (environment.FEATURE_FLAG_CAPTURE_NOTES) return null;

              return (
                <Link asChild href="/channel/new" className="mr-4">
                  <TouchableOpacity accessibilityRole="button" aria-label="create channel">
                    <PlusCircleIcon className="w-7 h-7 text-brand-subtle" />
                  </TouchableOpacity>
                </Link>
              );
            },
          }}
        />

        <Tabs.Screen
          name="create"
          options={{
            href: environment.FEATURE_FLAG_CAPTURE_NOTES ? undefined : null,
            tabBarButton: environment.FEATURE_FLAG_CAPTURE_NOTES ? CreateChannelBottomSheet : undefined,
          }}
        />

        <Tabs.Screen
          name="settings"
          options={{
            tabBarLabel: t('settings.title'),
            title: t('settings.title'),
            tabBarIcon: CogIcon,
            headerTitle: t('settings.title'),
            headerTitleAlign: 'center',
            headerShown: true,
          }}
        />
      </Tabs>
    </BottomSheet.Root>
  );
};

export default TabsLayout;
