import React, { useState } from 'react';
import * as ProgressBar from '@shape-construction/arch-ui/src/ProgressBar';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useMediaQuery } from '@shape-construction/hooks';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import Popover from '@shape-construction/arch-ui/src/Popover';
import { useMessageGetter } from '@messageformat/react';

interface IssueCardQualityIndicatorProps {
  className?: string;
  qualityScore: number | null;
}

export const IssueCardQualityIndicator: React.FC<IssueCardQualityIndicatorProps> = ({
  className,
  qualityScore,
}) => {
  const messages = useMessageGetter('dataBook.page.heatmapDashboard');
  const [qualityScorePopoverOpen, setQualityScorePopoverOpen] = useState(false);
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  // Only render if quality score is less than 30
  if (qualityScore === null || qualityScore >= 30) {
    return null;
  }

  console.log('qualityScore', qualityScore);

  return (
    <div className={cn('flex items-center justify-center p-1 bg-neutral-subtle hover:bg-neutral rounded-full', className)}>
      <Popover open={qualityScorePopoverOpen} onOpenChange={setQualityScorePopoverOpen}>
        <Popover.Trigger
          onPointerEnter={() => isLargeScreen && setQualityScorePopoverOpen(true)}
          onPointerLeave={() => isLargeScreen && setQualityScorePopoverOpen(false)}
        >
          <ProgressBar.Root
            progress={25}
            variant="donut"
            color="danger"
            size="small"
          />
        </Popover.Trigger>
        <Popover.Content side="top">
          <div className="flex flex-col gap-1 text-sm leading-6 font-medium">
            <span>{messages('healthLevels.1.label')}</span>
            <span className="font-bold">{messages('healthLevels.1.issues.scoreRange')}</span>
            <span className="font-normal">{messages('healthLevels.1.issues.description')}</span>
          </div>
        </Popover.Content>
      </Popover>
    </div>
  );
};

export default IssueCardQualityIndicator;
