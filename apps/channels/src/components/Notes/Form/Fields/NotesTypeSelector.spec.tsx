import { Controller, FormProvider, useForm } from 'react-hook-form';
import { render, renderHook, screen } from 'src/tests/test-utils';
import { NotesTypeSelector } from './NotesTypeSelector';

describe('<NotesTypeSelector />', () => {
  it('displays "issue" selected by default', async () => {
    const { result: form } = renderHook(() => useForm({ defaultValues: { type: 'issue' } }), undefined);

    render(
      <FormProvider {...form.current}>
        <Controller
          name="type"
          control={form.current.control}
          render={({ field }) => <NotesTypeSelector onValueChange={(option) => field.onChange(option?.value)} />}
        />
      </FormProvider>
    );

    expect(await screen.findByRole('combobox', { name: 'notes.newNote.types.issue' })).toBeOnTheScreen();
  });
});
