import 'src/global.css';
import 'src/localization/i18n';

import { useEffect } from 'react';
import { PortalProvider } from '@gorhom/portal';
import * as Sentry from '@sentry/react-native';
import { theme } from '@shape-construction/arch-ui-native/src/theme';
import { ThemeProvider } from '@shape-construction/arch-ui-native/src/theme/Theme';
import { PersistQueryClientProvider } from '@tanstack/react-query-persist-client';
import Constants, { ExecutionEnvironment } from 'expo-constants';
import { type ErrorBoundaryProps, Slot, useNavigationContainerRef } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import * as WebBrowser from 'expo-web-browser';
import { useTranslation } from 'react-i18next';
import { KeyboardAvoidingView, Platform, Text, TouchableOpacity } from 'react-native';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { RootSiblingParent } from 'react-native-root-siblings';
import { SafeAreaProvider, SafeAreaView } from 'react-native-safe-area-context';
import { registerForegroundEvent, registerNotificationsInteraction } from 'src/get-stream/notifications';
import { asyncStoragePersister, queryClient } from 'src/react-query/query-client';
import { environment } from '../config/environment';

WebBrowser.maybeCompleteAuthSession();

const navigationIntegration = Sentry.reactNavigationIntegration({
  enableTimeToInitialDisplay: Constants.executionEnvironment === ExecutionEnvironment.StoreClient,
});

Sentry.init({
  dsn: environment.SENTRY_DSN,
  environment: environment.APP_ENV,
  integrations: [navigationIntegration],
  enableNativeFramesTracking: Constants.executionEnvironment === ExecutionEnvironment.StoreClient,
  release: environment.VERSION,
});

export const RootLayout = () => {
  const ref = useNavigationContainerRef();

  useEffect(() => {
    registerNotificationsInteraction();
    return registerForegroundEvent();
  }, []);

  useEffect(() => {
    if (!environment.SENTRY_DSN) return;
    if (!ref) return;

    navigationIntegration.registerNavigationContainer(ref);
  }, [ref]);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <StatusBar style="dark" backgroundColor={theme.colors.white} />
      <PersistQueryClientProvider client={queryClient} persistOptions={{ persister: asyncStoragePersister }}>
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          className="flex-1 bg-white justify-center items-stretch"
        >
          <ThemeProvider theme={theme}>
            <PortalProvider>
              <RootSiblingParent>
                <SafeAreaProvider>
                  <Slot />
                </SafeAreaProvider>
              </RootSiblingParent>
            </PortalProvider>
          </ThemeProvider>
        </KeyboardAvoidingView>
      </PersistQueryClientProvider>
    </GestureHandlerRootView>
  );
};

export function ErrorBoundary({ retry }: ErrorBoundaryProps) {
  const { t } = useTranslation();

  return (
    <SafeAreaView className="flex-1 flex flex-col gap-4 justify-center items-center">
      <Text className="text-lg leading-5 font-bold">{t('errors.unexpected.title')}</Text>
      <Text className="text-normal leading-5">{t('errors.unexpected.description')}</Text>
      <TouchableOpacity
        accessibilityRole="button"
        className="py-3 px-4 rounded bg-brand-bold disabled:opacity-50"
        onPress={retry}
      >
        <Text className="text-white text-center text-sm leading-5 font-medium">
          {t('errors.unexpected.actions.tryAgain')}
        </Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
}

export default Sentry.wrap(RootLayout);
