import { getApiProjectsQueryOptions } from '@shape-construction/api/src/hooks';
import type { ProjectSchema } from '@shape-construction/api/src/types/projectSchema';
import { useQuery } from '@tanstack/react-query';
import { Stack } from 'expo-router';
import { FormProvider } from 'react-hook-form';
import { ActivityIndicator, StyleSheet } from 'react-native';
import { useSession } from 'src/authentication/SessionProvider';
import { useNotesForm } from 'src/components/Notes/Form/NotesForm';

function NewNoteLayoutComponent({ defaultProject }: { defaultProject?: ProjectSchema }) {
  const form = useNotesForm({ projectId: defaultProject?.id, type: 'issue' });

  return (
    <FormProvider {...form}>
      <Stack screenOptions={{ headerShown: false }} />
    </FormProvider>
  );
}

export default function NewNoteLayout() {
  const { user } = useSession();
  const { data: projects, isLoading } = useQuery(getApiProjectsQueryOptions());
  const defaultProject = projects?.find((project) => project.id === user.defaultProject);

  if (isLoading) return <ActivityIndicator style={StyleSheet.absoluteFill} />;

  return <NewNoteLayoutComponent defaultProject={defaultProject} />;
}
