import type { ComponentProps } from 'react';
import { getApiProjectsQueryOptions } from '@shape-construction/api/src/hooks';
import { Select } from '@shape-construction/arch-ui-native';
import { useQuery } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { useNotesFormContext } from '../NotesForm';

export const NotesProjectSelector = (props: ComponentProps<typeof Select.Root>) => {
  const { t } = useTranslation();
  const form = useNotesFormContext();
  const { data: projects } = useQuery(getApiProjectsQueryOptions());
  const selectedProject = projects?.find((project) => project.id === form.watch('projectId'));
  const selectedProjectOption: ComponentProps<typeof Select.Root>['value'] = selectedProject
    ? {
        value: selectedProject?.id,
        label: selectedProject?.title,
      }
    : undefined;

  return (
    <Select.Root value={selectedProjectOption} {...props}>
      <Select.Trigger>
        <Select.Value placeholder={t('notes.newNote.form.project.placeholder')} />
      </Select.Trigger>
      <Select.Content>
        {projects?.map((project) => (
          <Select.Item key={project.id} value={project.id} label={project.title} />
        ))}
      </Select.Content>
    </Select.Root>
  );
};
