import React from 'react';
import * as ProgressBar from '@shape-construction/arch-ui/src/ProgressBar';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';

interface IssueCardQualityIndicatorProps {
  className?: string;
  qualityScore: number | null;
}

export const IssueCardQualityIndicator: React.FC<IssueCardQualityIndicatorProps> = ({
  className,
  qualityScore,
}) => {
  // Only render if quality score is less than 30
  if (qualityScore === null || qualityScore >= 30) {
    return null;
  }

  return (
    <div className={cn('flex items-center justify-center', className)}>
      <ProgressBar.Root
        progress={qualityScore}
        variant="donut"
        color="danger"
        size="small"
      />
    </div>
  );
};

export default IssueCardQualityIndicator;
