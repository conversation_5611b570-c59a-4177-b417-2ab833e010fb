import type { ComponentProps } from 'react';
import { useTranslation } from 'react-i18next';
import { TextInput } from 'react-native';

export const NotesObservationInput = (props: ComponentProps<typeof TextInput>) => {
  const { t } = useTranslation();

  return (
    <TextInput
      {...props}
      accessibilityRole="text"
      className="p-2 border rounded border-input bg-white h-40"
      placeholder={t('notes.newNote.form.observation.placeholder')}
      multiline
      textAlignVertical="top"
    />
  );
};
