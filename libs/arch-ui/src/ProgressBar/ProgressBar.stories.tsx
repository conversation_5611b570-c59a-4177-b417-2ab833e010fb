import type { Meta, StoryObj } from '@storybook/react-vite';
import {
  type ProgressBarActiveColor,
  ProgressBarBadge,
  ProgressBarBadgeTheme,
  ProgressBarHeader,
  type ProgressBarProps,
  ProgressBarRoot,
  type ProgressBarSize,
  ProgressBarSubtitle,
  ProgressBarTitle,
} from './ProgressBar';

const progressBarColors: ProgressBarActiveColor[] = ['primary', 'success', 'warning', 'danger', 'secondary'];
const progressBarSizes: ProgressBarSize[] = ['small', 'medium', 'large'];

const meta: Meta<typeof ProgressBarRoot> = {
  title: 'Status/ProgressBar',
  component: ProgressBarRoot,
};
export default meta;
type Story = StoryObj<ProgressBarProps>;

export const Default: Story = {
  args: {
    progress: 50,
    size: 'medium',
  },
  render: (args) => <ProgressBarRoot {...args} />,
};

export const Colors: Story = {
  args: {
    progress: 50,
    size: 'medium',
  },
  render: (args) => (
    <div className="flex flex-col gap-4">
      {progressBarColors.map((color, index) => (
        <div key={color} className="flex flex-col gap-1">
          <span className="text-neutral">{color}</span>
          <ProgressBarRoot {...args} color={color} progress={index * 10 + 20} />
        </div>
      ))}
    </div>
  ),
};

export const Sizes: Story = {
  args: {
    progress: 50,
  },
  render: (args) => (
    <div className="flex flex-col gap-4">
      {progressBarSizes.map((size, index) => (
        <div key={size} className="flex flex-col gap-1">
          <span className="text-white">{size}</span>
          <ProgressBarRoot {...args} size={size} progress={index * 10 + 20} />
        </div>
      ))}
    </div>
  ),
};

export const WithPercentage: Story = {
  args: {
    progress: 50,
  },
  render: (args) => (
    <div className="flex flex-col gap-4">
      {progressBarSizes.map((size, index) => (
        <div key={size} className="flex flex-col gap-1">
          <ProgressBarRoot {...args} size={size} progress={index * 10 + 20}>
            <ProgressBarHeader showProgress>
              <ProgressBarTitle>{size}</ProgressBarTitle>
            </ProgressBarHeader>
          </ProgressBarRoot>
        </div>
      ))}
    </div>
  ),
};

export const WithTitleAndSubtitle: Story = {
  args: {
    progress: 50,
  },
  render: (args) => (
    <ProgressBarRoot {...args}>
      <ProgressBarHeader showProgress>
        <ProgressBarTitle>Title</ProgressBarTitle>
        <ProgressBarSubtitle>Subtitle</ProgressBarSubtitle>
      </ProgressBarHeader>
    </ProgressBarRoot>
  ),
};

export const WithTitle: Story = {
  args: {
    progress: 50,
  },
  render: (args) => (
    <ProgressBarRoot {...args}>
      <ProgressBarHeader showProgress>
        <ProgressBarTitle>Title</ProgressBarTitle>
      </ProgressBarHeader>
    </ProgressBarRoot>
  ),
};

export const WithBadge: Story = {
  args: {
    progress: 50,
  },
  render: (args) => (
    <ProgressBarRoot {...args}>
      <ProgressBarHeader showProgress>
        <ProgressBarTitle>
          Subtitle
          <ProgressBarBadge label="title" theme={ProgressBarBadgeTheme.BLUE} />
        </ProgressBarTitle>
        <ProgressBarSubtitle>
          Subtitle
          <ProgressBarBadge label="subtitle" theme={ProgressBarBadgeTheme.GREEN} />
        </ProgressBarSubtitle>
      </ProgressBarHeader>
    </ProgressBarRoot>
  ),
};
export const DarkMode: Story = {
  args: {
    progress: 50,
  },
  render: (args) => (
    <div data-mode="dark" className="bg-gray-800 p-10">
      <ProgressBarRoot {...args}>
        <ProgressBarHeader showProgress>
          <ProgressBarTitle>Title</ProgressBarTitle>
          <ProgressBarSubtitle>Subtitle</ProgressBarSubtitle>
        </ProgressBarHeader>
      </ProgressBarRoot>
    </div>
  ),
};

export const DonutDefault: Story = {
  args: {
    progress: 50,
    size: 'medium',
    variant: 'donut',
  },
  render: (args) => <ProgressBarRoot {...args} />,
};

export const DonutColors: Story = {
  args: {
    progress: 50,
    size: 'medium',
    variant: 'donut',
  },
  render: (args) => (
    <div className="flex flex-wrap gap-8">
      {progressBarColors.map((color, index) => (
        <div key={color} className="flex flex-col gap-2 items-center">
          <span className="text-neutral text-sm">{color}</span>
          <ProgressBarRoot {...args} color={color} progress={index * 15 + 25} />
        </div>
      ))}
    </div>
  ),
};

export const DonutSizes: Story = {
  args: {
    progress: 50,
    variant: 'donut',
  },
  render: (args) => (
    <div className="flex flex-wrap gap-8 items-center">
      {progressBarSizes.map((size, index) => (
        <div key={size} className="flex flex-col gap-2 items-center">
          <span className="text-white text-sm">{size}</span>
          <ProgressBarRoot {...args} size={size} progress={index * 20 + 30} />
        </div>
      ))}
    </div>
  ),
};

export const DonutWithTitleAndSubtitle: Story = {
  args: {
    progress: 75,
    variant: 'donut',
    size: 'large',
  },
  render: (args) => (
    <ProgressBarRoot {...args}>
      <ProgressBarHeader showProgress>
        <ProgressBarTitle>Project Progress</ProgressBarTitle>
        <ProgressBarSubtitle>3 of 4 tasks completed</ProgressBarSubtitle>
      </ProgressBarHeader>
    </ProgressBarRoot>
  ),
};

export const DonutDarkMode: Story = {
  args: {
    progress: 65,
    variant: 'donut',
    size: 'medium',
  },
  render: (args) => (
    <div data-mode="dark" className="bg-gray-800 p-10">
      <ProgressBarRoot {...args}>
        <ProgressBarHeader showProgress>
          <ProgressBarTitle>Dark Mode Progress</ProgressBarTitle>
          <ProgressBarSubtitle>Almost there!</ProgressBarSubtitle>
        </ProgressBarHeader>
      </ProgressBarRoot>
    </div>
  ),
};

export const DonutComparison: Story = {
  args: {
    progress: 60,
    size: 'medium',
  },
  render: (args) => (
    <div className="flex gap-8 items-center">
      <div className="flex flex-col gap-2 items-center">
        <span className="text-white text-sm">Linear</span>
        <ProgressBarRoot {...args} variant="linear">
          <ProgressBarHeader showProgress>
            <ProgressBarTitle>Linear Progress</ProgressBarTitle>
          </ProgressBarHeader>
        </ProgressBarRoot>
      </div>
      <div className="flex flex-col gap-2 items-center">
        <span className="text-white text-sm">Donut</span>
        <ProgressBarRoot {...args} variant="donut">
          <ProgressBarHeader showProgress>
            <ProgressBarTitle>Donut Progress</ProgressBarTitle>
          </ProgressBarHeader>
        </ProgressBarRoot>
      </div>
    </div>
  ),
};
