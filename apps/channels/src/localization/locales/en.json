{"all": "All", "actions": {"back": "Back", "cancel": "Cancel", "create": "Create", "next": "Next", "ok": "Ok", "yes": "Yes", "done": "Done", "save": "Save", "clear": "Clear", "retry": "Retry", "settings": "Go to settings"}, "create": {"title": "Create menu", "channel": {"title": "New chat"}, "note": {"title": "New note", "description": "Record progress or issues. Your team will see it instantly and use it in reports."}}, "errors": {"unexpected": {"title": "Unexpected error", "description": "Something went wrong. Please try again.", "actions": {"tryAgain": "Try again"}}, "sessionExpired": "You've been inactive for a while and your session expired"}, "permissions": {"mediaLibrary": {"denied": "You've refused to allow this app to access your gallery."}, "camera": {"denied": "You've refused to allow this app to access your camera."}, "location": {"denied": "You've refused to allow this app to access your location."}, "notifications": {"title": "Turn On Notifications", "subtitle": "You will need to enable the notification permissions from the settings page, please enable the permission and return back.", "banner": {"message": "Turn on notifications to get updates from your teams in Channels.", "actions": {"turnOn": "Turn on"}}, "sheet": {"title": "Turn on notifications", "subtitle": "Stay in sync with your team and allow Channels to send you notifications.", "benefits": {"receiveMessages": "Get notified when you receive messages", "addedToTeam": "Know when you're added to a team or group", "changeAnytime": "You can change this anytime in settings"}, "actions": {"goToSettings": "Open Settings"}}, "appSettingsSheet": {"title": "Turn on message alerts", "instructions": {"step1": "1. Open Channels App settings", "step2": "2. Tap notifications", "step3": "3. Toggle “Mute app notification” off"}, "toggleLabel": "Mute app notifications", "actions": {"goToSettings": "Go to settings"}}}}, "authForm": {}, "auth": {"title": "Shape Channels", "subtitle": "Use your existing Shape account", "loading": "Loading...", "form": {"email": "Email", "firstName": "First name", "lastName": "Last name", "password": "Password", "notYou": "Not you?", "controls": {"continue": "Continue", "or": "Or", "login": "<PERSON><PERSON>", "signInWithGoogle": "Continue with Google", "signInWithMicrosoft": "Continue with Microsoft", "noAccount": "Don't have an account?", "needHelp": "Need help?"}, "errors": {"root": "We can't find your Shape/Channels account"}}, "sso": {"title": "Log in with Google or Microsoft", "subtitle": "Your organisation requires Single Sign-On (SSO) for authentication. Please log in using your Google or Microsoft account to continue.", "backTo": "Go back"}}, "channelList": {"title": "Channels", "search": "Search for a channel or person", "timeout": {"title": "Connection is taking longer than expected", "description": "Please check your internet connection", "connectionFailed": "Failed to connect with the server, please try again."}}, "settings": {"title": "Settings", "logout": "Log out", "helpcenter": "Help center", "notifications": "Notifications", "logoutConfirmation": {"title": "Log out", "description": "Are you sure you want to log out?"}, "profile": {"title": "Edit profile", "uploadImage": "Upload image", "info": "Enter your name and an optional profile picture.", "edit": "Edit", "firstName": "First name", "lastName": "Last name", "email": "Email", "emailInfo": {"title": "Why can't I update my email?", "content": "You may update your email in the Shape web app. Log in your account and go to My account > Account details > Email > Edit"}, "placeholder": {"firstName": "Add first name", "lastName": "Add last name", "email": "Add email"}, "states": {"success": "Profile updated", "error": "Error on updating profile"}}}, "notes": {"newNote": {"title": "New note", "types": {"progress": "Progress", "issue": "Issue"}, "form": {"observation": {"title": "Observation", "placeholder": "What do you want to capture?"}, "project": {"placeholder": "Select a project"}, "notesType": {"title": "Type", "placeholder": "Select type"}, "actions": {"saveNote": "Save note"}, "feedback": {"success": "Note saved successfully", "error": "Failed to save note: {{error}}"}}}}, "notifications": {"title": "Notifications", "muteAppNotifications": "Mute app notifications", "permissionBanner": {"title": "Notification are disabled", "subtitle": "You won't receive updates unless you enable notifications in your phone settings.", "action": {"goToPhoneSettings": "Go to phone settings"}}}, "channel": {"events": {"delete": "The group was deleted"}, "error": "Error on loading channel", "projectSelect": "Choose project", "emptyMessages": "Awfully quiet in here...", "welcomeOneOnOneChatMessage": "Welcome to your direct chat with <bold>{{channelTitle}}</bold>.", "welcomeTeamChannelMessage": "Welcome to the <bold>{{channelTitle}}</bold> channel! This channel is auto-generated from the Shape project.", "welcomePersonalChannelMessage": {"title": "Welcome to your personal notes space!", "description": "You can save messages by forwarding them here and are backed up like any regular chat. Accessible from any device."}, "addedToChannelMessage": "You were added to this {{channelType}} channel, and only people involved in the project can access the messages shared here.", "welcomeGroupChannelMessage": "Welcome to the <bold>{{channelTitle}}</bold> channel!", "tapToAddGroupMembers": "Tap here to add new people", "tapToInviteTeamMembers": "Tap here to invite new people", "tapForSettings": "Tap here for group settings", "sentFile": "Sent a file", "sentImage": "Sent an image", "sentVideo": "Sent a video", "preview": {"autoTag": "Auto"}, "details": {"actions": {"edit": "Edit", "deleteGroup": "Delete group"}, "channelInfo": "Channel info", "deleteConfirmation": {"title": "Delete for all", "subTitle": "Do you want to delete the group and all its messages for everyone? This action cannot be undone.", "cancelCTA": "*actionCancel", "deleteCTA": "*actionDelete", "error": "Error deleting the group", "success": "Group deleted successfully"}, "edit": {"title": "Edit group", "addPhoto": "Add photo", "editPhoto": "Edit photo", "namePlaceholder": "Group name", "success": "Group updated successfully", "error": "Error updating the group", "actions": {"takePhoto": "Take a photo", "choosePhoto": "Choose photo", "removePhoto": "Remove photo", "save": "Done"}}, "error": "Error on loading channel details", "channelType": {"team": "Organisation", "group": "Group", "messaging": "Private", "personal": "Personal"}, "notificationSettings": {"title": "Notification settings", "actions": {"muteNotifications": "Mute notifications"}}, "invite": {"new": {"error": {"message": "Error on creating link."}, "title": "Invite others to join this channel and your organisation in Shape", "description": "You can set the links expiration and maximum number of people who can join.", "proWarning": "Pro plan: Any additional user that joins your organisation will imply additional cost.", "fields": {"expirationTime": {"label": "Expiration", "hint": "days"}, "peopleCount": {"label": "Maximum no. of people"}, "submit": "Generate invitation link"}, "submitConfirm": {"title": "Replace previous link?", "description": "Generating a new link will deactivate the old one. Are you sure you want to replace it?", "actions": {"cancel": "Cancel", "ok": "Ok"}}}, "actions": {"addMembers": "Add members", "copyLink": "Copy link", "expireLink": "Expire link", "generateLink": "Generate new link", "inviteMembers": {"title": "Invite new people", "description": "Existing Channels member"}, "notificationSettings": "Notification settings", "qrCode": "QR code", "shareLink": "Share link"}, "clipboardCopy": "Link copied to clipboard", "expirePrompt": {"title": "Are you sure you want to expire this link?"}, "information": {"expiredDescription": "This link has expired or reached the maximum number of people.", "linkExpired.0": "This link has", "linkExpired.1": "expired", "activeDescription": "Use this link to invite others to join this channel and your organisation in Shape", "linkExpireAt.0": "This link will expire in", "linkExpireAt.1": "{{ expireAt, date, short }}", "peopleCount.0": "{{current}} out of {{total}} people", "peopleCount.1": "have joined through this link.", "generateInformation": "You will need to generate a new link once it expired or reached the maximum number of people. ", "joinedInformation": "People who joined this organisation shall be managed in the Shape platform."}, "qrcode": {"title": "Invite via QR", "description": "Share this QR code to invite others to join this channel and your organisation in Shape", "actions": {"share": "Share QR code", "save": "Save QR code"}, "download": {"error": {"message": "Error on saving QR code to photo library."}, "success": {"message": "The QR code has been saved to your photo library."}}}}, "members": "{{count}} members", "addMembers": {"title": "Add members", "alreadySelected": "Already added to the group", "includeMembers": "Add members", "cancel": "Cancel", "search": "Search", "notifications": {"error": "Error adding members"}}, "moderator": "Moderator", "mediaSection": {"title": "Auto-upload media and documents to Shape gallery", "subtitle": "If on, all files uploaded in this channel will automatically be added to <PERSON><PERSON><PERSON>'s gallery."}, "updateMembersRole": {"notifications": {"memberToModeratorSuccess": "{{member}} is now a moderator", "memberNoLongerModeratorSuccess": "{{member}} is no longer a moderator", "updateMembersRoleError": "Failed to update members role"}}, "removeMembers": {"notifications": {"removeMemberSuccess": "{{username}} removed {{member}} from the group", "removeMemberError": "Failed to remove member"}, "removeMemberConfirmation": {"confirmRemove": "Remove", "title": "Remove member", "description": "Are you sure you want to remove {{member}} from this group?"}}}, "messages": {"actions": {"saveToGallery": "Save to <PERSON><PERSON><PERSON> gallery", "saveToGallerySuccess": "Attachments saved to gallery"}, "reactions": {"selector": "Reaction Selector on long pressing message", "listPanel": {"tapToRemove": "Tap to remove"}}}, "newChannel": {"title": "Select contact", "searchPlaceholder": "Search", "newGroup": "New group", "errorMessages": {"creatingChannelError": "Error on creating channel", "differentTeamError": "Only contacts from the same project can be added to the group."}}, "newGroup": {"projectStep": {"title": "Choose project"}, "membersStep": {"title": "Add members", "search": "Search", "membersCount": "Members: {{count}}"}, "infoStep": {"title": "Channel info", "uploadImage": "Upload image", "selectedUsers": "Selected users", "gorupNamePlaceholder": "Group name (optional)", "membersCount": "Members: {{count}}"}}, "mediaOptions": {"photos": "Photos", "camera": "Camera", "documents": "Documents"}, "actions": {"quickIssue": "Quick issue"}}, "helpcenter": {"title": "Help center", "articlesList": {"title": "How can we help you?"}, "article": {"title": "Help article"}, "articles": [{"id": "1", "title": "How do I invite other people to join Channels?", "supportingText": ["1. Go to the Channel of the Organisation for which you want to invite new users. Click on the top to access the management page of this Channel.", "2. On that page, you'll find the 'Invite new users' option at the bottom. Click on it and then select 'Generate new link'. On the next screen, you can set the link's validity and the number of people who can use it. Once done, click the 'Generate invitation link' at the bottom to complete the process.", "3. Click on 'Share link' and choose the medium you'll use to share the link, for example, through an email, text message, or WhatsApp."]}, {"id": "2", "title": "I got an invitation to join Channels. Now what?", "supportingText": ["1. You'll be prompted to enter your credentials when clicking the invitation link. Once you do this, you'll have a quick tour of Shape, after which you'll be suggested to install Channels on your phone if you haven't done so already", "2. Choose Google Play or Apple Store to download Channels, depending on your device.", "3. Once you download the app, you will have direct access to it and the channel you have been invited to join."]}, {"id": "3", "title": "What is the difference between automatically created Channels and the ones I make?", "supportingText": ["When you first use the app, channels are already created for you. The Shape Platform creates these and allows you to communicate with each organisation you are part of. You will have a channel for each project you use Shape for collaboration and record keeping. In addition to these automatically generated channels, you can create your own groups to communicate with whomever you want in your projects."]}, {"id": "4", "title": "How do Channels connect to the Shape World?", "supportingText": ["Every photo you and people in your organisation share in automatically created Channels is stored directly in your Project Gallery. This means you will no longer have evidence of progress, blockers, or snags scattered across multiple devices and never available when needed. Photos will be stored in a centralised location and easily used to provide evidence of progress reports in a Shift Report or of a blocker in the Issue Tracker."]}], "footer": {"title": "Have more questions?", "description.0": "If you encounter any problem or need assistance, please send an email to", "description.1": "{{email}}"}, "noAccount": {"headerTitle": "Shape account", "title": "Don't have an account yet?", "description.0": "You need an existing Shape account to access Channels. You may create a new account in the website using a browser.", "description.1": "You may also ask your Shape project's administrator to invite you through Shape or Channels.", "visitHelpCenter": "Visit our help center", "contact": "Contact support via email"}}, "states": {"error": "Error", "success": "Success"}, "noResults": {"title": "No results found", "description": "Check your spelling or try another keyword"}, "mediaPicker": {"title": "Select Image", "camera": "Take a photo", "library": "Chose photo"}, "groupMemberOptions": {"removeFromGroup": "Remove from group", "makeGroupModerator": "Make a group moderator", "dismissModerator": "Dismiss as moderator"}, "locationPicker": {"title": "Location", "addLocation": "Add location", "useRecentLocation": "Use recent location", "selectedLocation": "Selected location", "searchPlaceholder": "Search location", "search": {"noResults": "No results found", "checkSpelling": "Check your spelling or try another keyword"}, "manageLocationsNote": "You can manage the locations in Shape.", "manageLocationsLabel": "Manage locations in Shape"}, "currentUserLabel": "(You)", "version": "Channels version {{version}}", "welcome": {"highlights": {"secure": {"title": "Secured setting", "description": "Communicate with your site and office teams in secured and dedicated channels"}, "gallery": {"title": "Synced gallery", "description": "Seamlessly upload files and photos into Shape's centralised gallery to build your reports"}, "team": {"title": "Invite people", "description": "Easily invite new people to your organisation by sending them an invitation link"}}}}