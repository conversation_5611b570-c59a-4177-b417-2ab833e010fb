import { projectFactory } from '@shape-construction/api/factories/projects';
import { userFactory } from '@shape-construction/api/factories/users';
import { getApiProjectsMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { Controller, FormProvider, useForm } from 'react-hook-form';
import { server } from 'src/tests/mock-server';
import { render, renderHook, screen, userEvent, waitFor } from 'src/tests/test-utils';
import { NotesProjectSelector } from './NotesProjectSelector';

describe('<NotesProjectSelector />', () => {
  describe('when default project is set', () => {
    it('displays the default project', async () => {
      const project = projectFactory({ id: 'project-0', title: 'Project 0' });
      const user = userFactory({ defaultProject: 'project-0' });
      const { result: form } = renderHook(() => useForm({ defaultValues: { projectId: 'project-0' } }), undefined, {
        user,
      });
      server.use(getApiProjectsMockHandler(() => [project]));

      render(
        <FormProvider {...form.current}>
          <Controller
            name="projectId"
            control={form.current.control}
            render={({ field }) => <NotesProjectSelector onValueChange={(option) => field.onChange(option?.value)} />}
          />
        </FormProvider>,
        undefined,
        { user }
      );

      expect(await screen.findByRole('combobox', { name: 'Project 0' })).toBeOnTheScreen();
    });
  });

  describe('when default project is not set', () => {
    it('displays the placeholder', async () => {
      const project = projectFactory({ id: 'project-0', title: 'Project 0' });
      server.use(getApiProjectsMockHandler(() => [project]));
      const { result: form } = renderHook(() => useForm({ defaultValues: { projectId: null } }));

      render(
        <FormProvider {...form.current}>
          <Controller
            name="projectId"
            control={form.current.control}
            render={({ field }) => <NotesProjectSelector onValueChange={(option) => field.onChange(option?.value)} />}
          />
        </FormProvider>
      );

      expect(await screen.findByText('notes.newNote.form.project.placeholder')).toBeOnTheScreen();
    });
  });

  describe('when user selects a project', () => {
    it('updates the form value', async () => {
      const user = userFactory({ defaultProject: null });
      const project = projectFactory({ id: 'project-0', title: 'Project 0' });
      server.use(getApiProjectsMockHandler(() => [project]));
      const { result: form } = renderHook(() => useForm());

      render(
        <FormProvider {...form.current}>
          <Controller
            name="projectId"
            control={form.current.control}
            render={({ field }) => <NotesProjectSelector onValueChange={(option) => field.onChange(option?.value)} />}
          />
        </FormProvider>,
        undefined,
        { user }
      );

      await userEvent.press(await screen.findByRole('combobox', { name: 'notes.newNote.form.project.placeholder' }));
      await userEvent.press(await screen.findByRole('option', { name: 'Project 0' }));

      await waitFor(() => expect(form.current.getValues('projectId')).toBe('project-0'));
    });
  });
});
