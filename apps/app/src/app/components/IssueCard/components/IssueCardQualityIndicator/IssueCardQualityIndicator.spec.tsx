import React from 'react';
import { render, screen } from '@testing-library/react';
import { IssueCardQualityIndicator } from './IssueCardQualityIndicator';

describe('IssueCardQualityIndicator', () => {
  it('renders donut progress when quality score is less than 30', () => {
    render(<IssueCardQualityIndicator qualityScore={25} />);
    
    // Check that the progress bar is rendered
    expect(screen.getByText('25%')).toBeInTheDocument();
  });

  it('does not render when quality score is 30 or higher', () => {
    const { container } = render(<IssueCardQualityIndicator qualityScore={30} />);
    
    expect(container.firstChild).toBeNull();
  });

  it('does not render when quality score is null', () => {
    const { container } = render(<IssueCardQualityIndicator qualityScore={null} />);
    
    expect(container.firstChild).toBeNull();
  });

  it('renders donut progress when quality score is 0', () => {
    render(<IssueCardQualityIndicator qualityScore={0} />);
    
    // Check that the progress bar is rendered
    expect(screen.getByText('0%')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <IssueCardQualityIndicator qualityScore={25} className="custom-class" />
    );
    
    expect(container.firstChild).toHaveClass('custom-class');
  });
});
