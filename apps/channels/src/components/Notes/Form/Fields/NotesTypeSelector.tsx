import type { ComponentProps } from 'react';
import { Select } from '@shape-construction/arch-ui-native';
import { useTranslation } from 'react-i18next';
import { useNotesFormContext } from '../NotesForm';

export const NotesTypeSelector = (props: ComponentProps<typeof Select.Root>) => {
  const { t } = useTranslation();
  const form = useNotesFormContext();
  const notesTypes = [
    { value: 'issue', label: t('notes.newNote.types.issue') },
    { value: 'progress', label: t('notes.newNote.types.progress') },
  ] as const;
  const selectedType = notesTypes.find((type) => type.value === form.watch('type'));

  return (
    <Select.Root disabled value={selectedType} {...props}>
      <Select.Trigger>
        <Select.Value placeholder={t('notes.newNote.form.notesType.placeholder')} />
      </Select.Trigger>
      <Select.Content>
        {notesTypes.map((type) => (
          <Select.Item key={type.value} value={type.value} label={type.label} />
        ))}
      </Select.Content>
    </Select.Root>
  );
};
