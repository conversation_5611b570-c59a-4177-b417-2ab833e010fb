import React from 'react';
import type { IssueListItemSchema, IssueSchema } from '@shape-construction/api/src/types';
import { DoubleCircleIcon } from '@shape-construction/arch-ui/src/Icons/custom';
import { cn } from '@shape-construction/arch-ui/src/utils/classes';
import { useIssueStatesLabels } from 'app/constants/IssueStates';
import { useCurrentProject } from 'app/contexts/currentProject';
import type { LocationBackToState } from 'app/hooks/useIssueDetailsBackNavigation';
import type { LocationDescriptor } from 'history';
import { Link, type Location, useLocation } from 'react-router';
import { addQueryParams } from '../../lib/utils/query-string';
import IssueCardDue from './components/IssueCardDue/IssueCardDue';
import IssueCardImpact from './components/IssueCardImpact/IssueCardImpact';
import IssueCardLocation from './components/IssueCardLocation/IssueCardLocation';
import IssueCardQualityIndicator from './components/IssueCardQualityIndicator/IssueCardQualityIndicator';
import IssueCardStatus from './components/IssueCardStatus/IssueCardStatus';
import IssueCardTitle from './components/IssueCardTitle/IssueCardTitle';
import IssueCardType, { type IssueSubcategory } from './components/IssueCardType/IssueCardType';
import IssueCardUniquRef from './components/IssueCardUniqueRef/IssueCardUniquRef';

type CardStyleStatuses = {
  critical: IssueSchema['critical'];
  hasUpdates: boolean;
  isSelected: boolean;
  widthClass: string;
  bordered: boolean;
};

export const cardClasses = {
  card: ({
    critical,
    isSelected,
    widthClass,
    bordered,
  }: Pick<CardStyleStatuses, 'critical' | 'isSelected' | 'widthClass' | 'bordered'>) =>
    cn('flex items-center p-4 gap-x-4 gap-y-2 bg-white relative rounded-md', {
      'border border-gray-200': bordered,
      'shadow-[inset_4px_0] shadow-red-500': critical,
      'ring-2 ring-indigo-500': isSelected,
      'flex-wrap': ['sm', 'md'].includes(widthClass),
    }),
  cardTitle: ({ widthClass, hasUpdates }: Pick<CardStyleStatuses, 'widthClass' | 'hasUpdates'>) =>
    cn({
      'mr-6': ['sm', 'md'].includes(widthClass) && hasUpdates,
      'flex-1': widthClass === 'lg',
      'w-full': widthClass !== 'lg',
    }),
  cardRef: ({ widthClass }: Pick<CardStyleStatuses, 'widthClass'>) =>
    cn({
      'min-w-max': widthClass !== 'lg',
      'w-24': widthClass === 'lg',
    }),
  cardType: ({ widthClass }: Pick<CardStyleStatuses, 'widthClass'>) =>
    cn({
      'min-w-max': widthClass !== 'lg',
      'w-32': widthClass === 'lg',
    }),
  cardLocation: ({ widthClass }: Pick<CardStyleStatuses, 'widthClass'>) =>
    cn({
      'min-w-max': widthClass !== 'lg',
      'w-48': widthClass === 'lg',
    }),
  cardDue: ({ widthClass }: Pick<CardStyleStatuses, 'widthClass'>) =>
    cn({
      'min-w-max': widthClass !== 'lg',
      'w-20': widthClass === 'lg',
    }),
  cardImpact: ({ widthClass }: Pick<CardStyleStatuses, 'widthClass'>) =>
    cn({
      'min-w-max': widthClass !== 'lg',
      'w-36': widthClass === 'lg',
    }),
  cardStatus: ({ widthClass }: Pick<CardStyleStatuses, 'widthClass'>) =>
    cn({
      'mr-4': widthClass === 'lg',
      'min-w-max': widthClass !== 'lg',
      'w-36': widthClass === 'lg',
    }),
  cardQualityIndicator: ({ widthClass }: Pick<CardStyleStatuses, 'widthClass'>) =>
    cn({
      'min-w-max': widthClass !== 'lg',
      'w-16': widthClass === 'lg',
    }),
};

interface IssuePageState {
  backTo: Location;
}

export interface IssueCardProps {
  issue: IssueListItemSchema;
  isSelected?: boolean;
  widthClass?: string;
  linkTo?: LocationDescriptor<LocationBackToState> & {
    state?: IssuePageState;
  };
  bordered?: boolean;
}

export const IssueCard: React.FC<IssueCardProps> = ({
  issue,
  isSelected = false,
  widthClass = 'sm',
  linkTo,
  bordered = false,
}) => {
  const project = useCurrentProject();
  const projectTimezone = project.timezone;
  const hasUpdates = issue.updates.unreadUpdatesCount.public > 0 || issue.updates.unreadUpdatesCount.team > 0;
  const issueStates = useIssueStatesLabels();
  const issueStatusLabel = issueStates[issue.currentState] ?? '';

  const { overdue, critical } = issue;
  const { search, pathname } = useLocation();
  const linkUrl = {
    pathname,
    search: addQueryParams(search, { issueId: issue.id }),
  };

  return (
    <Link
      aria-current={isSelected}
      aria-label="issue card"
      data-testid={`issue-card-${issue.id}`}
      className={cardClasses.card({ critical, isSelected, widthClass, bordered })}
      to={linkTo || linkUrl}
      state={linkTo?.state}
    >
      <IssueCardTitle className={cardClasses.cardTitle({ widthClass, hasUpdates })} content={issue.title ?? ''} />
      <IssueCardUniquRef className={cardClasses.cardRef({ widthClass })} uniqueRef={issue.referenceNumber} />
      <IssueCardType
        className={cardClasses.cardType({ widthClass })}
        type={issue.category}
        subType={issue.subCategory as IssueSubcategory}
      />
      <IssueCardLocation className={cardClasses.cardLocation({ widthClass })} issue={issue} />
      <IssueCardDue
        className={cardClasses.cardDue({ widthClass })}
        timezone={projectTimezone}
        isOverdue={overdue}
        dueDate={issue.dueDate ? issue.dueDate : null}
      />
      <IssueCardImpact className={cardClasses.cardImpact({ widthClass })} impact={issue.impact} />
      <IssueCardQualityIndicator
        className={cardClasses.cardQualityIndicator({ widthClass })}
        qualityScore={issue.qualityScore}
      />
      <IssueCardStatus
        className={cardClasses.cardStatus({ widthClass })}
        text={issueStatusLabel}
        status={issue.currentState}
      />
      {hasUpdates && (
        <div role="status" aria-label="has updates" className="absolute top-4 right-4 text-blue-400">
          <DoubleCircleIcon width="24" height="24" selected={false} />
        </div>
      )}
    </Link>
  );
};
