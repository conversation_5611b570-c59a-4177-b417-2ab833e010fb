import { ChevronLeftIcon } from '@shape-construction/arch-ui-native/src/Icons/solid';
import { Stack, useRouter } from 'expo-router';
import { t } from 'i18next';
import { Controller } from 'react-hook-form';
import { Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { NotesObservationInput } from 'src/components/Notes/Form/Fields/NotesObservationInput';
import { NotesProjectSelector } from 'src/components/Notes/Form/Fields/NotesProjectSelector';
import { NotesTypeSelector } from 'src/components/Notes/Form/Fields/NotesTypeSelector';
import { useNotesFormContext, useNotesFormSubmit } from 'src/components/Notes/Form/NotesForm';

export default function NewNotePage() {
  const router = useRouter();
  const form = useNotesFormContext();
  const onSubmit = useNotesFormSubmit();

  return (
    <SafeAreaView edges={['bottom']} className="flex-1 justify-center items-center bg-neutral-subtlest">
      <View className="w-full flex-1">
        <Stack.Screen
          options={{
            headerShown: true,
            title: t('notes.newNote.title'),
            headerLeft: () => (
              <TouchableOpacity accessibilityRole="button" onPress={router.back}>
                <ChevronLeftIcon className="text-black -left-2" />
              </TouchableOpacity>
            ),
          }}
        />
        <View className="p-4 flex-1 flex flex-col justify-between">
          <View className="flex-1 flex flex-col gap-4">
            {/* Project selector */}
            <Controller
              name="projectId"
              control={form.control}
              render={({ field }) => <NotesProjectSelector onValueChange={(option) => field.onChange(option?.value)} />}
            />

            {/* Description input */}
            <View className="flex flex-col gap-2">
              <Text className="text-sm leading-5 font-bold text-neutral-subtle">
                {t('notes.newNote.form.observation.title')}
              </Text>
              <Controller
                name="observation"
                control={form.control}
                render={({ field }) => <NotesObservationInput autoFocus {...field} onChangeText={field.onChange} />}
              />
            </View>

            {/* Notes type */}
            <View className="flex flex-col gap-2">
              <Text className="text-sm leading-5 font-bold text-neutral-subtle">
                {t('notes.newNote.form.notesType.title')}
              </Text>
              <Controller
                name="type"
                control={form.control}
                render={({ field }) => <NotesTypeSelector onValueChange={(option) => field.onChange(option?.value)} />}
              />
            </View>
          </View>

          {/* Save button */}
          <TouchableOpacity
            accessibilityRole="button"
            disabled={!form.formState.isValid || form.formState.isSubmitting}
            className="py-3 px-4 rounded bg-brand-bold disabled:opacity-50"
            onPress={onSubmit}
          >
            <Text className="text-white text-center text-sm leading-5 font-medium">
              {t('notes.newNote.form.actions.saveNote')}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </SafeAreaView>
  );
}
